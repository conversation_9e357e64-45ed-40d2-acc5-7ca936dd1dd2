import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'https://tapi.ge0.cc',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加token
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理业务错误和token过期
api.interceptors.response.use(
  response => {
    const data = response.data

    // 检查业务错误状态
    if (data && (data.code === -1 || data.status === -1)) {
      // 业务错误，抛出错误
      return Promise.reject(new Error(data.message || data.msg || '操作失败'))
    }

    // 检查认证状态
    if (data && (data.code === 401 || data.status === 401)) {
      // 后端返回的认证失败
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('refresh_token')

      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login'
      }
      return Promise.reject(new Error(data.message || data.msg || '认证失败'))
    }

    return data
  },
  error => {
    // 检查错误响应中的认证信息
    const errorData = error.response?.data

    if (error.response?.status === 401 ||
        (errorData && (errorData.code === 401 || errorData.status === 401)) ||
        (errorData && errorData.message && errorData.message.includes('身份验证会话已过期'))) {

      // token过期，清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('refresh_token')

      // 只有在不是登录页面时才跳转
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  }
)

// 登录API
export const login = (username, password) => {
  return api.post('/frontApi/login', {
    username,
    password
  })
}

// 注册API
export const register = (userData) => {
  return api.post('/frontApi/register', userData)
}

// 获取用户信息API
export const getUserInfo = () => {
  return api.get('/frontApi/user')
}

// 登出API
export const logout = () => {
  return api.get('/frontApi/logout')
}

// 更新用户信息API
export const updateUserProfile = (profileData) => {
  return api.post('/frontApi/user/profile', profileData)
}

// 上传头像API
export const uploadAvatar = (formData) => {
  return api.post('/frontApi/user/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 提交邀请信息API
export const submitInvite = ({ inviterId, inviterName, invitedPhone, invitedName, appPackage }) => {
  return api.post('/app/yaoqing/submit', {
    inviter_id: inviterId,
    inviter_nickname: inviterName,
    invitee_phone: invitedPhone,
    invitee_nickname: invitedName,
    app_package: appPackage
  })
}

// 获取邀请记录列表API
export const getInviteList = (params = {}) => {
  return api.get('/app/yaoqing/list', { params })
}

// 获取应用列表API
export const getAppComName = () => {
  return api.get('/app/yaoqing/getAppComName')
}

// 获取邀请统计数据API
export const getInviteStatistics = () => {
  return api.get('/app/yaoqing/statistics')
}

export default api
