<template>
  <div class="container">
    <!-- 顶部栏：标题和用户信息 -->
    <div v-if="authStore.isAuthenticated" class="top-header">
      <h1 class="page-title">用户邀请</h1>
      <div class="top-bar">
        <div class="user-dropdown" ref="dropdownRef">
          <!-- 用户头像触发器 -->
          <div class="user-avatar-trigger" @click="toggleDropdown">
            <img v-if="authStore.user?.avatar" :src="getAvatarUrl(authStore.user.avatar)" :alt="authStore.user.nickname" class="avatar-img" />
            <div v-else class="avatar-placeholder">
              {{ (authStore.user?.nickname || authStore.user?.username)?.charAt(0)?.toUpperCase() || 'U' }}
            </div>
            <span class="user-name">{{ authStore.user?.nickname || authStore.user?.username }}</span>
            <svg class="dropdown-arrow" :class="{ 'rotate': showDropdown }" viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M7 10l5 5 5-5z"/>
            </svg>
          </div>

          <!-- 下拉菜单 -->
          <div v-if="showDropdown" class="dropdown-menu" @click.stop>
            <div class="dropdown-item" @click="viewProfile">
              <svg class="item-icon" viewBox="0 0 24 24" width="16" height="16">
                <path fill="currentColor" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
              个人信息
            </div>
            <div class="dropdown-item" @click="handleLogout">
              <svg class="item-icon" viewBox="0 0 24 24" width="16" height="16">
                <path fill="currentColor" d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
              </svg>
              退出登录
            </div>
          </div>

          <!-- 点击外部关闭下拉菜单 -->
          <div v-if="showDropdown" class="dropdown-backdrop" @click="closeDropdown"></div>
        </div>
      </div>
    </div>

    <div class="card" :class="{ 'logged-in': authStore.isAuthenticated }">
      <!-- 未登录时显示header -->
      <div v-if="!authStore.isAuthenticated" class="header">
        <div class="logo">
          <img src="/logo.png" alt="Logo" width="48" height="48" />
        </div>
        <h1 class="title">APP 用户邀请</h1>
        <p class="subtitle">邀请好友加入我们的应用</p>
      </div>

      <!-- Tabs 导航 -->
      <div v-if="authStore.isAuthenticated" class="tabs-container">
        <div class="tabs-nav">
          <button
              class="tab-item"
              :class="{ active: activeTab === 'invite' }"
              @click="switchTab('invite')"
          >
            邀请用户
          </button>
          <button
              class="tab-item"
              :class="{ active: activeTab === 'records' }"
              @click="switchTab('records')"
          >
            邀请记录
          </button>
          <button
              class="tab-item"
              :class="{ active: activeTab === 'data' }"
              @click="switchTab('data')"
          >
            邀请数据
          </button>
        </div>
      </div>

      <!-- 邀请用户 Tab 内容 -->
      <form v-if="activeTab === 'invite'" class="invite-form" @submit.prevent="handleSubmit">
        <!-- 应用选择 -->


        <div class="form-group">
          <label for="invitedPhone" class="label">
            <span class="label-text">被邀请人手机</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'invitedPhone' || form.invitedPhone }">
            <input
                type="tel"
                id="invitedPhone"
                v-model="form.invitedPhone"
                class="input"
                placeholder="请输入11位手机号码"
                required
                maxlength="11"
                pattern="[0-9]{11}"
                @input="handlePhoneInput"
                @focus="focusedField = 'invitedPhone'"
                @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="invitedName" class="label">
            <span class="label-text">被邀请人昵称或姓名</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'invitedName' || form.invitedName }">
            <input
                type="text"
                id="invitedName"
                v-model="form.invitedName"
                class="input"
                placeholder="请输入昵称或姓名"
                required
                @focus="focusedField = 'invitedName'"
                @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="inviterName" class="label">
            <span class="label-text">邀请人在APP昵称</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'inviterName' || form.inviterName }">
            <input
                type="text"
                id="inviterName"
                v-model="form.inviterName"
                class="input"
                placeholder="请输入您的APP昵称"
                required
                @focus="focusedField = 'inviterName'"
                @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="inviterId" class="label">
            <span class="label-text">邀请人APP ID</span>
            <span class="required">*</span>
            <!-- 提示信息 -->
            <small v-if="!hasLatestInviterId" class="id-hint">
              仔细检查ID，第一次填写后绑定
            </small>
          </label>
          <div class="input-wrapper" :class="{
            focused: focusedField === 'inviterId' || form.inviterId,
            disabled: hasLatestInviterId
          }">
            <input
                type="text"
                id="inviterId"
                v-model="form.inviterId"
                class="input"
                :placeholder="hasLatestInviterId ? '已自动填充' : '请输入8位APP ID'"
                required
                maxlength="8"
                pattern="[0-9]{8}"
                :readonly="hasLatestInviterId"
                :disabled="hasLatestInviterId"
                @input="handleAppIdInput"
                @focus="focusedField = 'inviterId'"
                @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="label">
            <span class="label-text">选择应用</span>
            <span class="required">*</span>
          </label>

          <div class="app-options" v-if="!isLoadingApps">
            <div v-if="appOptions.length === 0" style="padding: 20px; text-align: center; color: #999;">
              没有可用的应用选项
            </div>
            <div class="app-buttons" v-else>
              <button
                  v-for="app in appOptions"
                  :key="app.value"
                  type="button"
                  class="app-button"
                  :class="{ selected: form.appPackage === app.value }"
                  @click="form.appPackage = app.value"
              >
                {{ app.name }}
              </button>
            </div>
            <div v-if="appOptions.length > 0 && !form.appPackage" class="app-hint">
              请选择一个应用
            </div>
          </div>
          <div v-else class="loading-apps">
            加载应用列表中...
          </div>
        </div>

        <button type="submit" class="submit-btn" :class="{ loading: isLoading }" :disabled="isLoading">
          <span class="btn-text">{{ isLoading ? '发送中...' : '发送邀请' }}</span>
          <div class="btn-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 2L11 13"/>
              <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
            </svg>
          </div>
        </button>

      </form>

      <!-- 邀请记录 Tab 内容 -->
      <div v-if="activeTab === 'records'" class="records-content">
        <div class="records-list">
          <!-- 表头 -->
          <div class="table-row table-header">
            <span>手机号</span>
            <span>注册昵称</span>
            <span>注册ID</span>
            <span>APP</span>
            <span>邀请时间</span>
            <span class="header-with-filter">
              <div class="custom-select" @click="toggleFilterDropdown">
                <div class="select-display">{{ getFilterText() }}</div>
                <div class="select-arrow" :class="{ 'rotate': showFilterDropdown }">▼</div>
                <div v-if="showFilterDropdown" class="select-options">
                  <div
                      v-for="option in filterOptions"
                      :key="option.value"
                      class="select-option"
                      :class="{ 'selected': recordsFilter === option.value }"
                      @click.stop="selectFilter(option.value)"
                  >
                    {{ option.text }}
                  </div>
                </div>
              </div>
            </span>
          </div>

          <div v-for="record in filteredRecords" :key="record.id" class="table-row table-data">
            <span>{{ record.phone }}</span>
            <span :class="{ 'registered-user': record.invitee_registered_nickname && record.invitee_registered_nickname !== '-' }">
              {{ record.invitee_registered_nickname || '-' }}
            </span>
            <span>
              {{ record.invitee_id || '-' }}
            </span>
            <span>{{ record.app_package_name || record.app_package }}</span>
            <span>{{ record.invite_time }}</span>
            <span class="sms-status" :class="`sms-${record.sms_task_status}`">
              {{ getSmsStatusText(record.sms_task_status) }}
            </span>
          </div>

          <div v-if="isLoadingRecords" class="loading-records">
            加载中...
          </div>

          <div v-if="!isLoadingRecords && filteredRecords.length === 0" class="no-records">
            暂无邀请记录
          </div>
        </div>
      </div>

      <!-- 邀请数据 Tab 内容 -->
      <div v-if="activeTab === 'data'" class="data-content">
        <!-- 主要统计卡片 -->
        <div class="main-stat-card">
          <div class="card-header">
            <span class="card-title">数据统计</span>
          </div>
          <div class="main-number-container">
            <span class="main-number">{{ inviteStats.total_invites }}</span>
            <span class="main-unit">人</span>
          </div>

          <div class="sub-stats">
            <div class="sub-stat-item">
              <div class="sub-label">成功 (人)</div>
              <div class="sub-number">{{ inviteStats.success_invites }}</div>
            </div>
            <div class="sub-stat-item">
              <div class="sub-label">待确认 (人)</div>
              <div class="sub-number">{{ inviteStats.pending_invites }}</div>
            </div>
            <div class="sub-stat-item">
              <div class="sub-label">已失效 (人)</div>
              <div class="sub-number">{{ inviteStats.failed_invites }}</div>
            </div>
            <div class="sub-stat-item">
              <div class="sub-label">近7天 (人)</div>
              <div class="sub-number">{{ inviteStats.recent_invites }}</div>
            </div>
          </div>
        </div>

        <!-- 统计卡片网格 -->
        <div class="stats-grid">
          <!-- 今日邀请 -->
          <div class="stat-card today-card today-dual-card">
            <div class="dual-stats-container">
              <div class="dual-stat-item">
                <div class="card-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                    <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                    <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <div class="card-title">今日邀请</div>
                <div class="card-number-container">
                  <span class="card-number">{{ inviteStats.today_invites }}</span>
                  <span class="card-unit">人</span>
                </div>
              </div>
              <div class="dual-stat-divider"></div>
              <div class="dual-stat-item">
                <div class="card-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                    <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                    <path d="m22 2-5 5" stroke="currentColor" stroke-width="2"/>
                    <path d="m17 2 5 5" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <div class="card-title">今日绑定</div>
                <div class="card-number-container">
                  <span class="card-number">{{ inviteStats.today_binds }}</span>
                  <span class="card-unit">人</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 成功率 -->
          <div class="stat-card rate-card">
            <div class="card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                <path d="M8 12l2 2 4-4" stroke="currentColor" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <div class="card-title">成功率</div>
            <div class="card-number-container">
              <span class="card-number">{{ inviteStats.success_rate }}</span>
              <span class="card-unit">%</span>
            </div>
          </div>

          <!-- 短信成功 -->
          <div class="stat-card success-card">
            <div class="card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" fill="currentColor"/>
                <path d="M9 11l2 2 4-4" stroke="white" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <div class="card-title">短信成功</div>
            <div class="card-number-container">
              <span class="card-number">{{ inviteStats.sms_sent_success }}</span>
              <span class="card-unit">条</span>
            </div>
          </div>

          <!-- 短信失败 -->
          <div class="stat-card failed-card">
            <div class="card-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" fill="currentColor"/>
                <path d="M8 8l8 8M16 8l-8 8" stroke="white" stroke-width="2"/>
              </svg>
            </div>
            <div class="card-title">短信失败</div>
            <div class="card-number-container">
              <span class="card-number">{{ inviteStats.sms_sent_failed }}</span>
              <span class="card-unit">条</span>
            </div>
          </div>

        </div>
      </div>
    </div>



    <!-- 个人信息模态框 -->
    <ProfileModal
        :show="showProfileModal"
        @close="closeProfileModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth.js'
import { useNotificationStore } from './stores/notification'
import { getAvatarUrl } from './utils/index.js'
import ProfileModal from './components/ProfileModal.vue'
import { submitInvite, getInviteList, getAppComName, getInviteStatistics } from './api/auth.js'

const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

// 下拉菜单相关
const showDropdown = ref(false)
const dropdownRef = ref(null)

// 个人信息模态框
const showProfileModal = ref(false)

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const closeDropdown = () => {
  showDropdown.value = false
}



const viewProfile = () => {
  closeDropdown()
  showProfileModal.value = true
}

const closeProfileModal = () => {
  showProfileModal.value = false
}

const handleLogout = async () => {
  if (confirm('确定要退出登录吗？')) {
    await authStore.logout()
    router.push('/login')
  }
  closeDropdown()
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  if (!(event.target as HTMLElement).closest('.user-dropdown')) {
    closeDropdown()
  }
}

// 获取应用列表
const fetchAppOptions = async () => {
  try {
    isLoadingApps.value = true
    const response = await getAppComName()

    let appData: AppOption[] = []
    let latestInviterId: any = null

    // 检查实际的数据结构
    const responseData = response as any
    if (responseData && responseData.code === 1) {
      // 获取应用列表数据
      if (Array.isArray(responseData.data)) {
        appData = responseData.data
      }
      // 获取latest_inviter_id
      latestInviterId = responseData.latest_inviter_id
    } else if (Array.isArray(response.data)) {
      // 如果直接返回数组
      appData = response.data
    } else if (response.data && response.data.code === 1) {
      // 如果是嵌套格式
      if (Array.isArray(response.data.data)) {
        appData = response.data.data
      }
      latestInviterId = response.data.latest_inviter_id
    } else {
      console.error('无法识别的数据格式:', response)
      return
    }

    appOptions.value = appData

    // 处理latest_inviter_id
    if (latestInviterId && latestInviterId !== null && latestInviterId !== '') {
      form.inviterId = String(latestInviterId)
      hasLatestInviterId.value = true
    } else {
      hasLatestInviterId.value = false
    }

  } catch (error) {
    console.error('获取应用列表失败:', error)
  } finally {
    isLoadingApps.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  fetchAppOptions()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

interface InviteForm {
  invitedPhone: string
  invitedName: string
  inviterName: string
  inviterId: string
  appPackage: string
}

interface AppOption {
  value: string
  name: string
  label: string
}

interface InviteRecord {
  id: string | number
  phone: string
  invitee_registered_nickname?: string
  invitee_id?: string
  app_package: string
  app_package_name?: string
  sms_task_status: number
  invite_time?: string
}

const form = reactive<InviteForm>({
  invitedPhone: '',
  invitedName: '',
  inviterName: '',
  inviterId: '',
  appPackage: ''
})

// 应用列表相关数据
const appOptions = ref<AppOption[]>([])
const isLoadingApps = ref(false)
const hasLatestInviterId = ref(false) // 标记是否有最新的邀请人ID

const focusedField = ref('')
const isLoading = ref(false)

// Tabs 相关数据
const activeTab = ref('invite')

// 监听tab切换，当切换到邀请记录或邀请数据时加载数据
const switchTab = (tab: string) => {
  activeTab.value = tab
  if (authStore.isAuthenticated) {
    if (tab === 'records') {
      loadInviteRecords()
    } else if (tab === 'data') {
      loadInviteStatistics()
    }
  }
}

// 统计数据接口定义
interface InviteStatsData {
  total_invites: number
  success_invites: number
  pending_invites: number
  failed_invites: number
  sms_sent_success: number
  sms_sent_failed: number
  success_rate: number
  recent_invites: number
  today_invites: number
  today_binds: number
  user_info: {
    user_id: number
    username: string
    nickname: string
  }
}

// 邀请统计数据
const inviteStats = reactive<InviteStatsData>({
  total_invites: 0,           // 总邀请数
  success_invites: 0,         // 成功邀请数
  pending_invites: 0,         // 待确认数
  failed_invites: 0,          // 已失效数
  sms_sent_success: 0,        // 短信发送成功数
  sms_sent_failed: 0,         // 短信发送失败数
  success_rate: 0,            // 成功率(%)
  recent_invites: 0,          // 最近7天邀请数
  today_invites: 0,           // 今日邀请数
  today_binds: 0,             // 今日绑定数
  user_info: {
    user_id: 0,
    username: '',
    nickname: ''
  }
})

// 邀请记录相关数据
const recordsFilter = ref('')
const showFilterDropdown = ref(false)
const isLoadingRecords = ref(false)
const filterOptions = [
  { value: '', text: '全部' },
  { value: '0', text: '未发' },
  { value: '1', text: '进行' },
  { value: '2', text: '成功' },
  { value: '3', text: '失败' }
]
const inviteRecords = reactive<InviteRecord[]>([])
const recordsPagination = reactive({
  page: 1,
  limit: 20,
  total: 0,
  pages: 0
})

const resetForm = (): void => {
  // 只清空被邀请人信息，保留邀请人信息和应用选择
  form.invitedPhone = ''
  form.invitedName = ''
  // 如果邀请人ID不是自动填充的，也清空它
  if (!hasLatestInviterId.value) {
    form.inviterId = ''
  }
  // 保持邀请人信息和应用选择不重置，方便连续邀请
}

const showNotification = (message: string, type: 'success' | 'error'): void => {
  notificationStore.showNotification(type, message)
}

// 处理手机号输入，只允许数字
const handlePhoneInput = (event: Event): void => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '') // 移除所有非数字字符
  form.invitedPhone = value.slice(0, 11) // 限制最多11位
}

// 处理APP ID输入，只允许数字
const handleAppIdInput = (event: Event): void => {
  // 如果是自动填充的ID，不允许修改
  if (hasLatestInviterId.value) {
    return
  }
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '') // 移除所有非数字字符
  form.inviterId = value.slice(0, 8) // 限制最多8位
}

const validateForm = (): boolean => {
  // 检查必填字段
  const requiredFields = ['invitedPhone', 'invitedName', 'appPackage']

  // 验证手机号格式（11位数字）
  if (form.invitedPhone && !/^1[3-9]\d{9}$/.test(form.invitedPhone)) {
    showNotification('请输入正确的11位手机号', 'error')
    return false
  }

  // 验证APP ID格式（8位数字）
  if (form.inviterId && !/^\d{8}$/.test(form.inviterId)) {
    showNotification('请输入正确的8位APP ID', 'error')
    return false
  }

  return requiredFields.every(field => form[field as keyof InviteForm].trim() !== '')
}

const submitInviteApi = async (): Promise<void> => {
  try {
    const res: any = await submitInvite({
      inviterId: form.inviterId,
      inviterName: form.inviterName,
      invitedPhone: form.invitedPhone,
      invitedName: form.invitedName,
      appPackage: form.appPackage
    })
    if (res.code === 1) {
      showNotification('邀请发送成功！', 'success')
      resetForm()
    } else {
      showNotification(res.msg || '发送失败，请重试', 'error')
    }
  } catch (error: any) {
    showNotification(error.message || '发送失败，请重试', 'error')
  }
}

const handleSubmit = async (): Promise<void> => {
  // 验证表单
  if (!validateForm()) {
    showNotification('请填写所有必填项', 'error')
    return
  }

  // 显示加载状态
  isLoading.value = true

  try {
    await submitInviteApi()
  } catch (error) {
    showNotification('发送失败，请重试', 'error')
  } finally {
    // 恢复按钮状态
    isLoading.value = false
  }
}

// 计算属性：直接返回邀请记录（筛选在后端完成）
const filteredRecords = computed(() => {
  return inviteRecords
})



// 获取SMS状态文本
const getSmsStatusText = (status: string | number) => {
  const statusMap: Record<string, string> = {
    '0': '发送',
    '1': '进行',
    '2': '成功',
    '3': '失败',
    'pending': '未发',
    'sending': '进行',
    'sent': '成功',
    'failed': '失败'
  }
  return statusMap[String(status)] || '未知'
}


// 加载邀请记录
const loadInviteRecords = async (page = 1) => {
  if (!authStore.isAuthenticated) return

  isLoadingRecords.value = true
  try {
    const params: any = {
      page: page,
      limit: recordsPagination.limit
    }

    // 添加短信状态筛选
    if (recordsFilter.value !== '') {
      params.sms_task_status = recordsFilter.value
    }

    const res = await getInviteList(params)
    if (res.data && res.data.list && Array.isArray(res.data.list)) {
      // 清空现有记录
      inviteRecords.splice(0, inviteRecords.length)
      // 添加新记录，只保留需要的字段
      const filteredRecords = res.data.list.map((item: any) => ({
        id: item.id,
        phone: item.phone,
        invitee_registered_nickname: item.invitee_registered_nickname || '-',
        invitee_id: item.invitee_id || '-',
        status: item.status,
        sms_task_status: item.sms_task_status,
        app_package: item.app_package,
        app_package_name: item.app_package_name || item.app_package,
        invite_time: item.invite_time
      }))
      inviteRecords.push(...filteredRecords)

      // 更新分页信息
      recordsPagination.page = res.data.page || 1
      recordsPagination.total = res.data.total || 0
      recordsPagination.pages = res.data.pages || 1
    } else {
      showNotification('加载邀请记录失败：数据格式错误', 'error')
    }
  } catch (error: any) {
    showNotification(error.message || '加载邀请记录失败', 'error')
  } finally {
    isLoadingRecords.value = false
  }
}

// 加载邀请统计数据
const loadInviteStatistics = async () => {
  if (!authStore.isAuthenticated) return

  try {
    const response: any = await getInviteStatistics()

    if (response && response.code === 1 && response.data) {
      // response就是后端返回的完整对象，response.data是统计数据
      Object.assign(inviteStats, response.data)
    } else {
      showNotification('加载统计数据失败：数据格式错误', 'error')
    }
  } catch (error: any) {
    showNotification(error.message || '加载统计数据失败', 'error')
  }
}

// 自定义下拉框相关方法
const toggleFilterDropdown = () => {
  showFilterDropdown.value = !showFilterDropdown.value
}

const selectFilter = (value: string) => {
  recordsFilter.value = value
  showFilterDropdown.value = false
  // 筛选条件改变时重新加载数据
  loadInviteRecords(1)
}

const getFilterText = () => {
  return '状态'
}
</script>

<style scoped>
/* 顶部标题栏样式 */
.top-header {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 340px;
  padding: 0;
  box-sizing: border-box;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 顶部用户栏样式 */
.top-bar {
  position: static;
}

.user-dropdown {
  position: relative;
  display: inline-block;
}

.user-avatar-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.3s;
  user-select: none;
}

.user-avatar-trigger:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #666;
  border: 2px solid #e0e0e0;
}

.user-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.dropdown-arrow {
  transition: transform 0.3s;
  color: #666;
}

.dropdown-arrow.rotate {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  color: #333;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.item-icon {
  color: #666;
}

.dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* 登录后的卡片样式调整 */
.card.logged-in {
  margin-top: 0;
}

/* 移动端适配 - 键盘弹起时避免重叠 */
@media (max-width: 768px) {
  .top-header {
    position: static;
    top: auto;
    left: auto;
    transform: none;
    margin-bottom: 20px;
    width: 90%;
    max-width: none;
    margin-left: auto;
    margin-right: auto;
    padding: 0;
  }


  .top-bar {
    position: static;
  }

  .dropdown-menu {
    right: 0px;
    margin-top: 2px;
    min-width: 140px;
  }
}

@media (max-width: 480px) {
  .top-header {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Tabs 样式 */
.tabs-container {
  position: sticky;
  top: 0;
  background: #ffffff;
  z-index: 999;
  margin-bottom: 24px;
}

.tabs-nav {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 0;
  background: #ffffff;
}

.tab-item {
  background: none;
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.tab-item:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}

.tab-item.active {
  color: #333;
  border-bottom-color: #333;
  font-weight: 600;
}

/* 邀请记录内容样式 */
.records-content {
  padding: 0;
}

.header-with-filter {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
}

.header-with-filter .custom-select {
  min-width: auto;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.header-with-filter .select-display {
  font-size: 12px;
  padding: 2px 20px 2px 8px;
  font-weight: 600;
  color: #333;
  background: transparent;
  border: none;
}

.header-with-filter .select-arrow {
  right: 4px;
  font-size: 8px;
}



.filter-select {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  min-width: 120px;
  outline: none;
}

.filter-select:focus {
  border-color: #333;
  box-shadow: 0 0 0 2px rgba(51, 51, 51, 0.1);
}

/* 自定义下拉框样式 */
.custom-select {
  position: relative;
  display: inline-block;
  min-width: 120px;
}

.select-display {
  padding: 8px 32px 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  user-select: none;
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #666;
  transition: transform 0.3s ease;
  pointer-events: none;
}

.select-arrow.rotate {
  transform: translateY(-50%) rotate(180deg);
}

.select-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 2px;
}

.select-option {
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.select-option:last-child {
  border-bottom: none;
}

.select-option:hover {
  background: #f8f9fa;
}

.select-option.selected {
  background: #e3f2fd;
  color: #1976d2;
}

.records-list {
  background: transparent;
}

/* 统一的表格行样式 */
.table-row {
  display: grid;
  grid-template-columns: 1.8fr 1.6fr 1.2fr 1.4fr 2.3fr 1.15fr;
  gap: 8px;
  width: 100%;
  font-size: 8px;
}

/* 表头特有样式 */
.table-header {
  padding: 4px 0;
  border-bottom: 2px solid #ddd;
  background: #f8f9fa;
  font-weight: 600;
  color: #555;
  margin-bottom: 4px;
}

.table-header span {
  display: flex;
  align-items: center;
  height: 100%;
}

/* 数据行特有样式 */
.table-data {
  padding: 6px 0;
  border-bottom: 1px solid #e9ecef;
  background: transparent;
  line-height: 1.2;
}

/* 数据列的颜色样式 */
.table-data span:nth-child(1) { /* 手机号 */
  font-weight: 500;
  color: #333;
}

.table-data span:nth-child(2), /* 注册昵称 */
.table-data span:nth-child(3), /* 注册ID */
.table-data span:nth-child(5) { /* 邀请时间 */
  color: #666;
}

.table-data span:nth-child(4) { /* APP名 */
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-data span:nth-child(6) { /* 状态列 */
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.sms-status {
  padding: 1px 5px;
  border-radius: 4px;
  font-size: 8px;
  white-space: nowrap;
  display: inline-block;
  text-align: center;
  width: fit-content;
  max-width: none;
  flex-shrink: 0;
}

/* SMS状态样式 */
.sms-status.sms-0 {
  background: #e6e6e6;
  color: #5c5c5c;
}

.sms-status.sms-1 {
  background: #fff3cd;
  color: #856404;
}

.sms-status.sms-2 {
  background: #d4edda;
  color: #155724;
}

.sms-status.sms-3 {
  background: #f8d7da;
  color: #721c24;
}

/* 已注册用户样式 */
.registered-user {
  color: #333 !important;
  font-weight: 500;
}

.loading-records {
  padding: 20px 0;
  text-align: center;
  color: #666;
  font-size: 8px;
}

.no-records {
  padding: 20px 0;
  text-align: center;
  color: #666;
  font-size: 8px;
}

/* PC端响应式样式 */
@media (min-width: 768px) {
  /* PC端整体容器加宽 */
  .top-header {
    max-width: 800px;
    width: 90%;
  }

  .tabs-container {
    width: 100%;
    max-width: none;
  }

  .records-list {
    width: 100%;
    max-width: none;
  }

  .table-row {
    font-size: 14px;
  }

  .table-header {
    padding: 6px 0;
  }

  .table-data {
    padding: 10px 0;
  }

  .record-phone,
  .record-nickname,
  .record-id,
  .record-package,
  .record-time {
    font-size: 14px;
  }

  .sms-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    width: fit-content;
    max-width: 100%;
  }

  .loading-records,
  .no-records {
    font-size: 14px;
  }

  /* Grid布局会自动处理列宽，不需要min-width设置 */
}

/* 邀请数据内容样式 */
.data-content {
  padding: 0;
}

/* 主要统计卡片 */
.main-stat-card {
  background: #70b27f;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 16px;
  color: rgb(255, 255, 255);
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
}

.main-number-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.main-number {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
}

.main-unit {
  font-size: 16px;
  opacity: 0.8;
  font-weight: 500;
}

.sub-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  position: relative;
  z-index: 1;
}

.sub-stat-item {
  text-align: center;
}

.sub-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 3px;
  line-height: 1;
}

.sub-number {
  font-size: 22px;
  font-weight: 600;
  line-height: 1;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.success-card .card-icon {
  background: #10b981;
  color: white;
}

.failed-card .card-icon {
  background: #ef4444;
  color: white;
}

.rate-card .card-icon {
  background: #8b5cf6;
  color: white;
}

.today-card .card-icon {
  background: #f59e0b;
  color: white;
}

.week-card .card-icon {
  background: #06b6d4;
  color: white;
}

.stat-card .card-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.card-number-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.card-number {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.card-unit {
  font-size: 14px;
  color: #9ca3af;
  font-weight: 500;
}

/* 双列统计卡片样式 */
.today-dual-card {
  position: relative;
}

.dual-stats-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin-top: -27px;
}

.dual-stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: -13px;
}

.dual-stat-item:first-child {
  padding-right: 10px;
}

.dual-stat-item:last-child {
  padding-left: 10px;
}

.dual-stat-item .card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  background: #f59e0b;
  color: white;
}

.dual-stat-item .card-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.dual-stat-item .card-number-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.dual-stat-item .card-number {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.dual-stat-item .card-unit {
  font-size: 14px;
  color: #9ca3af;
  font-weight: 500;
}

.dual-stat-divider {
  width: 1px;
  height: 80px;
  background: #e5e7eb;
  margin: 0 12px;
  align-self: center;
}

/* 双列统计卡片样式 */
.today-dual-card {
  position: relative;
}

.dual-stats-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 12px;
}

.dual-stat-item {
  flex: 1;
  text-align: center;
}

.dual-stat-item .card-title {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 6px;
  font-weight: 500;
}

.dual-stat-item .card-number-container {
  display: flex;
  align-items: baseline;
  gap: 2px;
  justify-content: center;
}

.dual-stat-item .card-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.dual-stat-item .card-unit {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.dual-stat-divider {
  width: 1px;
  height: 40px;
  background: #e5e7eb;
  margin: 0 12px;
}

/* 应用选择样式 */
.app-options {
  margin-top: 8px;
}

.app-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.app-button {
  padding: 6px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  background: white;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  white-space: nowrap;
}

.app-button:hover {
  border-color: #333;
  background: #f8f9fa;
  color: #333;
}

.app-button.selected {
  border-color: #a7cf88;
  background: #a7cf88;
  color: white;
}

.app-button:active {
  transform: scale(0.98);
}

.app-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  text-align: center;
}

/* 移动端应用按钮样式 */
@media (max-width: 768px) {
  .app-buttons {
    justify-content: center;
  }

  .app-button {
    min-width: 60px;
    text-align: center;
  }
}

.loading-apps {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .tabs-container {
    position: sticky;
    top: 0;
    background: #ffffff;
    z-index: 999;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
  }

  .tabs-nav {
    margin-bottom: 0;
  }

  .tab-item {
    padding: 10px 16px;
    font-size: 14px;
  }

  .records-content,
  .data-content {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
  }

  .custom-select {
    min-width: 100px;
  }

  .header-with-filter .custom-select {
    min-width: auto;
    width: 100%;
  }

  .header-with-filter .select-display {
    font-size: 10px;
    padding: 2px 16px 2px 4px;
    font-weight: 600;
  }

  .header-with-filter .select-arrow {
    right: 2px;
    font-size: 6px;
  }

  .select-display {
    font-size: 12px;
    padding: 6px 28px 6px 10px;
  }

  .select-arrow {
    right: 10px;
    font-size: 10px;
  }

  .select-option {
    font-size: 12px;
    padding: 6px 10px;
  }

  .main-stat-card {
    padding: 20px;
    margin-bottom: 12px;
  }

  .main-number {
    font-size: 40px;
  }

  .main-unit {
    font-size: 14px;
  }

  .sub-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
  }

  .sub-label {
    font-size: 10px;
    margin-bottom: 2px;
  }

  .sub-number {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .stat-card {
    padding: 16px;
  }

  .card-number {
    font-size: 24px;
  }

  .card-unit {
    font-size: 12px;
  }

  /* 移动端双列卡片样式 */
  .dual-stat-item:first-child {
    padding-right: 8px;
  }

  .dual-stat-item:last-child {
    padding-left: 8px;
  }

  .dual-stat-item .card-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    margin-bottom: 12px;
  }

  .dual-stat-item .card-title {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .dual-stat-item .card-number {
    font-size: 24px;
  }

  .dual-stat-item .card-unit {
    font-size: 12px;
  }

  .dual-stat-divider {
    height: 70px;
    margin: 0 8px;
  }
}

@media (max-width: 480px) {
  .tabs-container {
    position: sticky;
    top: 0;
    background: #ffffff;
    z-index: 999;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
  }

  .tabs-nav {
    margin-bottom: 0;
  }

  .tab-item {
    padding: 8px 12px;
    font-size: 14px;
  }

  .records-content,
  .data-content {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
  }

  .custom-select {
    min-width: 90px;
  }

  .header-with-filter .custom-select {
    min-width: auto;
    width: 100%;
  }

  .header-with-filter .select-display {
    font-size: 9px;
    padding: 2px 14px 2px 3px;
    font-weight: 600;
  }

  .header-with-filter .select-arrow {
    right: 1px;
    font-size: 5px;
  }

  .select-display {
    font-size: 11px;
    padding: 5px 24px 5px 8px;
  }

  .select-arrow {
    right: 8px;
    font-size: 9px;
  }

  .select-option {
    font-size: 11px;
    padding: 5px 8px;
  }

  .main-stat-card {
    padding: 16px;
    margin-bottom: 10px;
  }

  .main-number {
    font-size: 36px;
  }

  .main-unit {
    font-size: 12px;
  }

  .sub-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
  }

  .sub-label {
    font-size: 9px;
    margin-bottom: 2px;
  }

  .sub-number {
    font-size: 14px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-card {
    padding: 14px;
  }

  .card-number {
    font-size: 22px;
  }

  .card-unit {
    font-size: 11px;
  }

  /* 小屏幕双列卡片样式 */
  .dual-stat-item:first-child {
    padding-right: 7px;
  }

  .dual-stat-item:last-child {
    padding-left: 7px;
  }

  .dual-stat-item .card-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    margin-bottom: 12px;
  }

  .dual-stat-item .card-title {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .dual-stat-item .card-number {
    font-size: 22px;
  }

  .dual-stat-item .card-unit {
    font-size: 11px;
  }

  .dual-stat-divider {
    height: 60px;
    margin: 0 6px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .sub-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }
}

/* 自动填充的输入框样式 */
.input:disabled,
.input:readonly {
  background: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed !important;
  border-color: #d9d9d9 !important;
  opacity: 0.8 !important;
}

/* 禁用状态的输入框包装器 */
.input-wrapper.disabled {
  opacity: 0.8;
}

.input-wrapper.disabled .input-icon {
  color: #ccc !important;
}

/* ID提示信息样式 */
.id-hint {
  color: #dc3545;
  font-size: 10px;
  font-weight: normal;
  margin-left: 8px;
}
</style>