<template>
  <div class="container">
    <div class="card">
      <div class="header">
        <div class="logo">
          <img src="/logo.png" alt="Logo" width="48" height="48" />
        </div>
        <h1 class="title">用户注册</h1>
        <p class="subtitle">创建您的新账户，开始精彩旅程</p>
      </div>

      <form class="register-form" @submit.prevent="handleRegister">
        <div class="form-group">
          <label for="username" class="label">
            <span class="label-text">用户名</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'username' || form.username }">
            <input
              type="text"
              id="username"
              v-model="form.username"
              class="input"
              placeholder="请输入用户名"
              required
              @focus="focusedField = 'username'"
              @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="phone" class="label">
            <span class="label-text">手机号码</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'phone' || form.phone }">
            <input
              type="tel"
              id="phone"
              v-model="form.phone"
              class="input"
              placeholder="请输入11位手机号码"
              required
              maxlength="11"
              pattern="[0-9]{11}"
              @input="handlePhoneInput"
              @focus="focusedField = 'phone'"
              @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="password" class="label">
            <span class="label-text">密码</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'password' || form.password }">
            <input
              :type="showPassword ? 'text' : 'password'"
              id="password"
              v-model="form.password"
              class="input"
              placeholder="请输入密码"
              required
              @focus="focusedField = 'password'"
              @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <circle cx="12" cy="16" r="1"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
              </svg>
            </div>
            <div class="password-toggle" @click="togglePassword">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path v-if="showPassword" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                <path v-if="showPassword" d="M1 1l22 22"/>
                <path v-if="!showPassword" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle v-if="!showPassword" cx="12" cy="12" r="3"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="confirmPassword" class="label">
            <span class="label-text">确认密码</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'confirmPassword' || form.confirmPassword }">
            <input
              :type="showConfirmPassword ? 'text' : 'password'"
              id="confirmPassword"
              v-model="form.confirmPassword"
              class="input"
              placeholder="请再次输入密码"
              required
              @focus="focusedField = 'confirmPassword'"
              @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M12 3v6l4-4-4-4"/>
              </svg>
            </div>
            <div class="password-toggle" @click="toggleConfirmPassword">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path v-if="showConfirmPassword" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                <path v-if="showConfirmPassword" d="M1 1l22 22"/>
                <path v-if="!showConfirmPassword" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle v-if="!showConfirmPassword" cx="12" cy="12" r="3"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-options">
          <label class="checkbox-wrapper">
            <input type="checkbox" v-model="form.agreeTerms" class="checkbox" required>
            <span class="checkbox-label">我同意 <a href="#" class="terms-link">用户协议</a> 和 <a href="#" class="terms-link">隐私政策</a></span>
          </label>
          <router-link to="/login" class="login-link-inline">立即登录</router-link>
        </div>

        <button type="submit" class="submit-btn" :class="{ loading: isLoading }" :disabled="isLoading">
          <span class="btn-text">{{ isLoading ? '注册中...' : '立即注册' }}</span>
          <div class="btn-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="8.5" cy="7" r="4"/>
              <line x1="20" y1="8" x2="20" y2="14"/>
              <line x1="23" y1="11" x2="17" y2="11"/>
            </svg>
          </div>
        </button>


      </form>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { register } from './api/auth.js'
import { useNotificationStore } from './stores/notification'

// 路由
const router = useRouter()
const notificationStore = useNotificationStore()

// 响应式数据
const focusedField = ref('')
const isLoading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 表单数据
const form = reactive({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 显示通知
const showNotification = (type: 'success' | 'error', message: string) => {
  notificationStore.showNotification(type, message)
}

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 切换确认密码显示
const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 处理手机号输入，只允许数字
const handlePhoneInput = (event: Event): void => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '') // 移除所有非数字字符
  form.phone = value.slice(0, 11) // 限制最多11位
}

// 验证表单
const validateForm = () => {
  if (!form.username || !form.phone || !form.password || !form.confirmPassword) {
    showNotification('error', '请填写完整的注册信息')
    return false
  }

  if (form.password !== form.confirmPassword) {
    showNotification('error', '两次输入的密码不一致')
    return false
  }

  if (form.password.length < 6) {
    showNotification('error', '密码长度至少6位')
    return false
  }

  if (!form.agreeTerms) {
    showNotification('error', '请同意用户协议和隐私政策')
    return false
  }

  // 手机号格式验证（11位，1开头）
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(form.phone)) {
    showNotification('error', '请输入正确的11位手机号')
    return false
  }

  return true
}

// 处理注册
const handleRegister = async () => {
  if (!validateForm()) {
    return
  }

  // 防止重复提交
  if (isLoading.value) {
    return
  }

  isLoading.value = true

  try {
    // 调用注册API
    const response = await register({
      username: form.username,
      email: form.email,
      password: form.password,
      mobile: form.phone
    })

    console.log('注册成功:', response)
    showNotification('success', '注册成功！即将跳转到登录页面')

    // 注册成功后跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (error: any) {
    // 响应拦截器已经处理了错误，直接使用error.message
    const errorMessage = error.message || '注册失败，请重试'
    console.log('显示错误信息:', errorMessage) // 调试信息
    showNotification('error', errorMessage)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.register-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: -8px 0 8px 0;
}

/* 移动端调整 - 确保form-options内容居中 */
@media (max-width: 768px) {
  .form-options {
    width: 90%;
    margin: -8px auto 8px auto;
    justify-content: space-between;
    padding: 0;
  }
}

@media (max-width: 480px) {
  .form-options {
    width: 90%;
    margin: -8px auto 8px auto;
    justify-content: space-between;
    padding: 0;
  }
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
  margin-top: 2px;
  flex-shrink: 0;
}

.checkbox-label {
  font-size: 14px;
  color: #666;
  user-select: none;
  line-height: 1.4;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

.login-link-inline {
  font-size: 14px;
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  margin-top: 2px;
  flex-shrink: 0;
}

.login-link-inline:hover {
  text-decoration: underline;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
  padding: 4px;
  border-radius: 4px;
}

.password-toggle:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}


</style>
