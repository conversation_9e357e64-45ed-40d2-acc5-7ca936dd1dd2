import { defineStore } from 'pinia'
import { login as login<PERSON>pi, getUserInfo, logout as logoutApi, updateUserProfile, uploadAvatar } from '../api/auth.js'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user') || 'null'),
    isLoggedIn: false
  }),

  getters: {
    isAuthenticated: (state) => !!state.token && !!state.user
  },

  actions: {
    // 登录
    async login(username, password) {
      try {
        const response = await loginApi(username, password)

        if (response.code === 1) {
          const { user, token } = response.data

          // 提取实际的access_token（参考demo项目）
          const accessToken = typeof token === 'object' ? token.access_token : token

          // 保存到状态
          this.token = accessToken
          this.user = user
          this.isLoggedIn = true

          // 保存到本地存储
          localStorage.setItem('token', accessToken)
          localStorage.setItem('user', JSON.stringify(user))

          // 如果有refresh_token，也保存起来
          if (typeof token === 'object' && token.refresh_token) {
            localStorage.setItem('refresh_token', token.refresh_token)
          }

          return { success: true, data: response.data }
        } else {
          return { success: false, message: response.msg }
        }
      } catch (error) {
        console.error('登录失败:', error)
        return {
          success: false,
          message: error.response?.data?.msg || '登录失败，请稍后重试'
        }
      }
    },

    // 获取用户信息
    async fetchUserInfo() {
      try {
        const response = await getUserInfo()
        if (response.code === 1) {
          this.user = response.data
          localStorage.setItem('user', JSON.stringify(response.data))
          return response.data
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.logout()
      }
    },

    // 登出
    async logout() {
      try {
        if (this.token) {
          await logoutApi()
        }
      } catch (error) {
        console.error('登出请求失败:', error)
      } finally {
        // 清除状态
        this.token = ''
        this.user = null
        this.isLoggedIn = false

        // 清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('user')
      }
    },

    // 初始化认证状态
    initAuth() {
      const token = localStorage.getItem('token')
      const user = localStorage.getItem('user')

      if (token && user) {
        this.token = token
        this.user = JSON.parse(user)
        this.isLoggedIn = true

        // 验证token是否有效
        this.fetchUserInfo()
      }
    },

    // 更新用户信息
    async updateProfile(profileData) {
      try {
        // console.log('正在更新用户信息:', profileData)
        // console.log('当前token:', this.token)

        const response = await updateUserProfile(profileData)

        if (response.code === 1) {
          // 更新本地用户信息
          this.user = { ...this.user, ...profileData }
          localStorage.setItem('user', JSON.stringify(this.user))

          return { success: true, message: response.msg || '更新成功' }
        } else {
          return { success: false, message: response.msg || '更新失败' }
        }
      } catch (error) {
        console.error('更新用户信息失败:', error)
        console.error('错误详情:', error.response?.data)

        // 如果是401错误，说明token过期或无效
        if (error.response?.status === 401) {
          return { success: false, message: '登录已过期，请重新登录' }
        }

        return {
          success: false,
          message: error.response?.data?.msg || error.response?.data?.message || '更新失败，请稍后重试'
        }
      }
    },

    // 上传头像
    async uploadAvatar(file) {
      try {
        const formData = new FormData()
        formData.append('avatar', file)

        const response = await uploadAvatar(formData)

        if (response.code === 1) {
          // 更新本地用户头像信息
          this.user.avatar = response.data.avatar_url
          localStorage.setItem('user', JSON.stringify(this.user))

          return {
            success: true,
            message: response.msg || '头像上传成功',
            data: response.data
          }
        } else {
          return { success: false, message: response.msg || '头像上传失败' }
        }
      } catch (error) {
        console.error('头像上传失败:', error)
        return {
          success: false,
          message: error.response?.data?.msg || '头像上传失败，请稍后重试'
        }
      }
    }
  }
})
