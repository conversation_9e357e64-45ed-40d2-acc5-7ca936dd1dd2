import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface NotificationItem {
  id: string
  type: 'success' | 'error'
  message: string
  duration?: number
}

export const useNotificationStore = defineStore('notification', () => {
  const notifications = ref<NotificationItem[]>([])

  const showNotification = (type: 'success' | 'error', message: string, duration = 3000) => {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 11)
    
    const notification: NotificationItem = {
      id,
      type,
      message,
      duration
    }

    notifications.value.push(notification)

    // 自动移除通知
    setTimeout(() => {
      removeNotification(id)
    }, duration)

    return id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAll = () => {
    notifications.value = []
  }

  return {
    notifications,
    showNotification,
    removeNotification,
    clearAll
  }
})
