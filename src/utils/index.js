// API基础URL
export const API_BASE_URL = 'https://tapi.ge0.cc'

/**
 * 获取完整的头像URL
 * @param {string} avatarPath - 头像路径
 * @returns {string} 完整的头像URL
 */
export const getAvatarUrl = (avatarPath) => {
  if (!avatarPath) return ''
  
  // 如果已经是完整URL，直接返回
  if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
    return avatarPath
  }
  
  // 拼接API域名
  return `${API_BASE_URL}${avatarPath}`
}

/**
 * 获取用户显示名称
 * @param {object} user - 用户对象
 * @returns {string} 显示名称
 */
export const getUserDisplayName = (user) => {
  return user?.nickname || user?.username || '未知用户'
}

/**
 * 获取用户头像占位符
 * @param {object} user - 用户对象
 * @returns {string} 头像占位符字符
 */
export const getAvatarPlaceholder = (user) => {
  const name = getUserDisplayName(user)
  return name.charAt(0).toUpperCase()
}
