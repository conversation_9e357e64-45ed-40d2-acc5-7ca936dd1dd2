// 声明Vue组件类型
declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 具体模块声明
declare module './stores/auth.js' {
  export function useAuthStore(): any
}

declare module './utils/index.js' {
  export function getAvatarUrl(url: string): string
}

declare module './api/auth.js' {
  export function submitInvite(data: any): Promise<any>
  export function getInviteList(params: any): Promise<any>
  export function getAppComName(): Promise<any>
  export function register(data: any): Promise<any>
  export function login(data: any): Promise<any>
}

// 通用JavaScript模块声明
declare module '*.js' {
  const content: any
  export = content
}
