<template>
  <div class="container">
    <div class="card">
      <div class="header">
        <div class="logo">
          <img src="/logo.png" alt="Logo" width="48" height="48" />
        </div>
        <h1 class="title">用户登录</h1>
        <p class="subtitle">欢迎回来，请登录您的账户</p>
      </div>

      <form class="login-form" @submit.prevent="handleLogin">
        <div class="form-group">
          <label for="username" class="label">
            <span class="label-text">用户名或邮箱</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'username' || form.username }">
            <input
              type="text"
              id="username"
              v-model="form.username"
              class="input"
              placeholder="请输入用户名或邮箱"
              required
              @focus="focusedField = 'username'"
              @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="password" class="label">
            <span class="label-text">密码</span>
            <span class="required">*</span>
          </label>
          <div class="input-wrapper" :class="{ focused: focusedField === 'password' || form.password }">
            <input
              :type="showPassword ? 'text' : 'password'"
              id="password"
              v-model="form.password"
              class="input"
              placeholder="请输入密码"
              required
              @focus="focusedField = 'password'"
              @blur="focusedField = ''"
            />
            <div class="input-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <circle cx="12" cy="16" r="1"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
              </svg>
            </div>
            <div class="password-toggle" @click="togglePassword">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path v-if="showPassword" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                <path v-if="showPassword" d="M1 1l22 22"/>
                <path v-if="!showPassword" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle v-if="!showPassword" cx="12" cy="12" r="3"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="form-options">
          <label class="checkbox-wrapper">
            <input type="checkbox" v-model="form.rememberMe" class="checkbox">
            <span class="checkbox-label">记住我</span>
          </label>
          <router-link to="/register" class="register-link-inline">立即注册</router-link>
        </div>

        <button type="submit" class="submit-btn" :class="{ loading: isLoading }" :disabled="isLoading">
          <span class="btn-text">{{ isLoading ? '登录中...' : '登录' }}</span>
          <div class="btn-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
              <polyline points="10,17 15,12 10,7"/>
              <line x1="15" y1="12" x2="3" y2="12"/>
            </svg>
          </div>
        </button>


      </form>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth.js'
import { useNotificationStore } from './stores/notification'

const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

// 响应式数据
const focusedField = ref('')
const isLoading = ref(false)
const showPassword = ref(false)

// 表单数据
const form = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 显示通知
const showNotification = (type: 'success' | 'error', message: string) => {
  notificationStore.showNotification(type, message)
}

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 处理登录
const handleLogin = async () => {
  if (!form.username || !form.password) {
    showNotification('error', '请填写完整的登录信息')
    return
  }

  isLoading.value = true

  try {
    const result = await authStore.login(form.username, form.password)

    if (result.success) {
      showNotification('success', '登录成功！')

      // 登录成功后跳转到首页
      setTimeout(() => {
        const redirect = router.currentRoute.value.query.redirect || '/'
        router.push(String(redirect))
      }, 1000)
    } else {
      showNotification('error', result.message || '登录失败')
    }

  } catch (error) {
    console.error('登录错误:', error)
    showNotification('error', '登录失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}




</script>

<style scoped>
.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: -8px 0 8px 0;
}

/* 移动端调整 - 确保form-options内容居中 */
@media (max-width: 768px) {
  .form-options {
    width: 90%;
    margin: -8px auto 8px auto;
    justify-content: space-between;
    padding: 0;
  }
}

@media (max-width: 480px) {
  .form-options {
    width: 90%;
    margin: -8px auto 8px auto;
    justify-content: space-between;
    padding: 0;
  }
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

.checkbox-label {
  font-size: 14px;
  color: #666;
  user-select: none;
}

.register-link-inline {
  font-size: 14px;
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.register-link-inline:hover {
  text-decoration: underline;
}

.test-account {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  font-size: 13px;
}

.test-account p {
  margin: 4px 0;
  color: #666;
}

.test-account p strong {
  color: #333;
}



.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
  padding: 4px;
  border-radius: 4px;
}

.password-toggle:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}


</style>
