<template>
  <div>
    <div v-if="show" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>个人信息</h2>
        <button class="close-btn" @click="closeModal">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>



      <div class="modal-body">
        <!-- 头像部分 -->
        <div class="avatar-section">
          <div class="avatar-container" :class="{ uploading: showAvatarUpload }" @click="showAvatarUpload = true">
            <img v-if="authStore.user?.avatar" :src="getAvatarUrl(authStore.user.avatar)" :alt="authStore.user.nickname" class="avatar-img" />
            <div v-else class="avatar-placeholder">
              {{ (authStore.user?.nickname || authStore.user?.username)?.charAt(0)?.toUpperCase() || 'U' }}
            </div>
            <!-- 悬停提示 -->
            <div class="avatar-overlay">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                <circle cx="12" cy="13" r="4"/>
              </svg>
              <span>点击更换</span>
            </div>
          </div>
        </div>

        <!-- 表单部分 -->
        <form class="profile-form" @submit.prevent="updateProfile">
          <div class="form-group">
            <label class="label">用户名</label>
            <input type="text" v-model="profileForm.username" class="input" disabled />
          </div>

          <div class="form-group">
            <label class="label">邮箱</label>
            <input type="email" v-model="profileForm.email" class="input" placeholder="请输入邮箱" />
          </div>

          <div class="form-group">
            <label class="label">手机号</label>
            <input type="tel" v-model="profileForm.mobile" class="input" placeholder="请输入手机号" />
          </div>

          <div class="form-group">
            <label class="label">昵称</label>
            <input type="text" v-model="profileForm.nickname" class="input" placeholder="请输入昵称" />
          </div>

          <div class="form-actions">
            <button type="submit" class="save-btn" :disabled="loading">
              {{ loading ? '保存中...' : '保存修改' }}
            </button>
            <button type="button" class="reset-btn" @click="resetForm">重置</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 头像上传对话框 -->
    <div v-if="showAvatarUpload" class="upload-overlay" @click="showAvatarUpload = false">
        <div class="upload-modal" @click.stop>
          <div class="upload-header">
            <h3>更换头像</h3>
            <button class="close-btn" @click="showAvatarUpload = false">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>
          <div class="upload-body">
            <div class="upload-area" @click="triggerFileInput">
              <input ref="fileInput" type="file" accept="image/*" @change="handleFileSelect" style="display: none" />
              <div class="upload-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="7,10 12,15 17,10"/>
                  <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
              </div>
              <p>点击选择图片</p>
              <p class="upload-tip">支持 JPG、PNG 格式，大小不超过 2MB</p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { useNotificationStore } from '../stores/notification'
import { getAvatarUrl } from '../utils/index.js'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

const authStore = useAuthStore()
const notificationStore = useNotificationStore()
const loading = ref(false)
const showAvatarUpload = ref(false)
const fileInput = ref(null)

// 表单数据
const profileForm = reactive({
  username: '',
  email: '',
  mobile: '',
  nickname: ''
})

// 显示通知
const showNotification = (type, message) => {
  notificationStore.showNotification(type, message)
}

// 初始化表单数据
const initForm = () => {
  if (authStore.user) {
    profileForm.username = authStore.user.username || ''
    profileForm.email = authStore.user.email || ''
    profileForm.mobile = authStore.user.mobile || ''
    profileForm.nickname = authStore.user.nickname || ''
  }
}

// 重置表单
const resetForm = () => {
  initForm()
}

// 更新用户信息
const updateProfile = async () => {
  // 简单验证
  if (profileForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileForm.email)) {
    showNotification('error', '请输入正确的邮箱地址')
    return
  }

  if (profileForm.mobile && !/^1[3-9]\d{9}$/.test(profileForm.mobile)) {
    showNotification('error', '请输入正确的手机号')
    return
  }

  loading.value = true

  try {
    const result = await authStore.updateProfile({
      email: profileForm.email,
      mobile: profileForm.mobile,
      nickname: profileForm.nickname
    })

    if (result.success) {
      showNotification('success', result.message)
    } else {
      showNotification('error', result.message)
    }
  } catch (error) {
    showNotification('error', '更新失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!['image/jpeg', 'image/png'].includes(file.type)) {
    showNotification('error', '头像只能是 JPG/PNG 格式!')
    return
  }

  // 验证文件大小
  if (file.size / 1024 / 1024 > 2) {
    showNotification('error', '头像大小不能超过 2MB!')
    return
  }

  uploadAvatar(file)
}

// 上传头像
const uploadAvatar = async (file) => {
  try {
    const result = await authStore.uploadAvatar(file)

    if (result.success) {
      showNotification('success', result.message)
      showAvatarUpload.value = false
    } else {
      showNotification('error', result.message)
    }
  } catch (error) {
    showNotification('error', '头像上传失败，请稍后重试')
  }
}

// 关闭模态框
const closeModal = () => {
  emit('close')
}

// 监听show属性变化，初始化表单
watch(() => props.show, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initForm()
    })
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 40px 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 85%;
  max-width: 320px;
  max-height: 70vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  transition: all 0.3s;
}

.close-btn:hover {
  background: #f5f5f5;
  color: #333;
}

.info-banner {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #fff3cd;
  border-bottom: 1px solid #ffeaa7;
  color: #856404;
  font-size: 13px;
}

.info-banner svg {
  flex-shrink: 0;
}

.modal-body {
  padding: 14px;
}

.avatar-section {
  text-align: center;
}

.avatar-container {
  margin-bottom: 8px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 70px;
  height: 70px;
  display: inline-block;
}

.avatar-container:hover {
  /* 移除缩放效果，避免PC端出现大椭圆遮罩 */
}

.avatar-img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.avatar-placeholder {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: #f0f0f0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: #666;
  border: 2px solid #e0e0e0;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
  gap: 4px;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

/* 当显示头像上传对话框时隐藏悬停覆盖层 */
.avatar-container.uploading .avatar-overlay {
  opacity: 0 !important;
}

.avatar-overlay span {
  font-size: 12px;
  font-weight: 500;
}



.profile-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input {
  padding: 10px 14px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  outline: none;
  transition: border-color 0.3s;
}

.input:focus {
  border-color: #667eea;
}

.input:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 6px;
}

.save-btn, .reset-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.save-btn {
  background: #667eea;
  color: white;
  border: none;
  flex: 1;
}

.save-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.save-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #e0e0e0;
}

.reset-btn:hover {
  background: #e8e8e8;
}

/* 头像上传对话框 */
.upload-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.upload-modal {
  background: white;
  border-radius: 12px;
  width: 85%;
  max-width: 300px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  border-bottom: 1px solid #e0e0e0;
}

.upload-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.upload-body {
  padding: 14px;
}

.upload-area {
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  padding: 24px 14px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.upload-icon {
  color: #999;
  margin-bottom: 12px;
}

.upload-area p {
  margin: 8px 0;
  color: #333;
  font-size: 14px;
}

.upload-tip {
  color: #999 !important;
  font-size: 12px !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .modal-overlay {
    align-items: center;
    padding: 30px 15px;
  }

  .modal-content {
    width: 95%;
    max-height: calc(100vh - 60px);
  }

  .modal-header, .modal-body {
    padding: 14px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
