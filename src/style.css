/* 重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* 禁用点击时的淡蓝色方块闪烁效果 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /*background: linear-gradient(135deg, #009aff 0%, #45cab0 100%);*/
  min-height: 100vh;
  overflow-x: hidden;
}

/* 为需要文本选择的元素恢复选择功能 */
input, textarea, [contenteditable] {
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 容器样式 */
.container {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

/* 背景动画 */
.background-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
}



/* 卡片样式 */
.card {
  background: none;
  backdrop-filter: none;
  border-radius: 0;
  padding: 40px;
  box-shadow: none;
  border: none;
  max-width: 340px;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* PC端卡片样式 */
@media (min-width: 768px) {
  .card {
    max-width: 900px;
    width: 90%;
    padding: 40px 60px;
  }
}



/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 20px;
  display: inline-block;
  padding: 12px;
  background: rgba(156, 156, 156, 0.1);
  border-radius: 16px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.logo img {
  display: block;
  border-radius: 8px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #656565 !important;
  margin-bottom: 8px;
  background: none !important;
  -webkit-text-fill-color: initial !important;
  background-clip: text;
}
.subtitle {
  font-size: 16px;
  color: #656565 !important;
  font-weight: 400;
  background: rgba(255,255,255,0.7);
  border-radius: 8px;
  padding: 4px 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  display: inline-block;
  margin-top: 8px;
  text-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

/* 表单样式 */
.invite-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #222 !important;
}

.label-text {
  color: #656565 !important;
}

.required {
  color: #e74c3c !important;
  font-weight: 700;
}

.input-wrapper {
  position: relative;
}

.input {
  width: 100%;
  padding: 14px 20px 14px 50px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 16px;
  background: #fff;
  outline: none;
  color: #222 !important;
}



.input::placeholder {
  color: #888 !important;
  font-weight: 400;
  opacity: 1;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.input-wrapper.focused .input-icon {
  color: #667eea;
}

/* 提交按钮样式 */
.submit-btn {
  color: #1d1d1d !important;
  font-weight: 600;
  text-shadow: none;
  border: 1px solid #a7cf88;
  box-shadow: 0 2px 8px rgba(167, 207, 136, 0.3);
  background-color: #a7cf88;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 36px auto 0;
  position: relative;
  overflow: hidden;
  width: auto;
  max-width: 200px;
  min-width: 160px;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: #95c070;
  border-color: #95c070;
  box-shadow: 0 4px 12px rgba(167, 207, 136, 0.4);
  transform: translateY(-1px);
}

.submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.submit-btn:disabled {
  background-color: #ccc;
  border-color: #ccc;
  color: #999 !important;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn.loading {
  background-color: #8fb56a;
  border-color: #8fb56a;
}

.submit-btn.loading .btn-icon {
  opacity: 0.7;
}

.btn-text {
  font-weight: 600;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    min-height: 100vh;
  }

  .card {
    background: none;
    backdrop-filter: none;
    border-radius: 0;
    padding: 32px 0;
    box-shadow: none;
    border: none;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 14px;
  }

  .input {
    padding: 12px 18px 12px 45px;
    font-size: 16px;
  }

  /* 统一所有表单元素的宽度和居中 */
  .form-group,
  .test-account,
  .submit-btn,
  .invite-form > *,
  .login-form > *,
  .register-form > * {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
  }

  .submit-btn {
    padding: 14px 20px;
    font-size: 16px;
  }


}

@media (max-width: 480px) {
  .container {
    padding: 8px 0;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    min-height: 100vh;
  }
  .card {
    background: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    padding: 0;
    max-width: 100vw;
    width: 100vw;
    margin: 0;
    border: none;
  }
  .header {
    margin-top: 32px;
    margin-bottom: 32px;
  }
  .invite-form {
    gap: 16px;
  }
  .input {
    padding: 12px 14px 12px 42px;
    font-size: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
    background: rgba(255,255,255,0.95);
  }

  /* 统一所有表单元素的宽度和居中 */
  .form-group,
  .test-account,
  .submit-btn,
  .invite-form > *,
  .login-form > *,
  .register-form > * {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
  }
  .submit-btn {
    padding: 14px 0;
    font-size: 16px;
    width: 100% !important;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(167, 207, 136, 0.3);
    margin-bottom: 32px;
    align-self: stretch !important;
    background-color: #a7cf88;
    border-color: #a7cf88;
  }
}



.header {
  margin-top: 24px;
  margin-bottom: 32px;
}



.input:focus {
  border-color: #57a392 !important;
  /*box-shadow: 0 0 0 2px rgb(206, 237, 230) !important;*/
}



/* 深色模式支持 - 暂时禁用 */
/* @media (prefers-color-scheme: dark) {
  .card {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .title {
    color: #fff;
  }

  .subtitle {
    color: #ccc;
  }

  .label-text {
    color: #fff;
  }

  .input {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: #fff;
  }

  .input::placeholder {
    color: #888;
  }

  .notification {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .notification span {
    color: #fff;
  }
} */



/* 焦点可见性 */
.input:focus-visible {
  outline: none;
}

.submit-btn:focus-visible {
  outline: 2px solid #a7cf88;
  outline-offset: 2px;
}

/* 页面链接样式 */
.page-link {
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

.page-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  margin-left: 4px;
}

.page-link a:hover {
  text-decoration: underline;
}
